import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function parseMarkdownSection(content: string, sectionName: string) {
  // Simple markdown section parser for comparison pages
  const lines = content.split("\n");
  let inSection = false;
  let sectionContent: string[] = [];

  for (const line of lines) {
    if (line.startsWith(`## ${sectionName}`)) {
      inSection = true;
      continue;
    }
    if (inSection && line.startsWith("## ")) {
      break;
    }
    if (inSection) {
      sectionContent.push(line);
    }
  }

  // Parse the section content into structured data
  const result: any = {};
  let currentKey = "";

  for (const line of sectionContent) {
    if (line.startsWith("### ")) {
      currentKey = line
        .replace("### ", "")
        .toLowerCase()
        .replace(/\s+/g, "");
      result[currentKey] = "";
    } else if (currentKey && line.trim()) {
      result[currentKey] = result[currentKey]
        ? `${result[currentKey]}\n${line}`
        : line;
    }
  }

  return result;
}
