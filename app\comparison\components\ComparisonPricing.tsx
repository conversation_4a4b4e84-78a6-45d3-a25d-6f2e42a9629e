"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";
import { Check } from "lucide-react";

interface ComparisonPricingProps {
  comparison: Comparison;
}

export default function ComparisonPricing({ comparison }: ComparisonPricingProps) {
  const pricingSection = parseMarkdownSection(comparison.content, "Pricing Section");
  
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Pricing Comparison
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compare pricing plans and see why Inloop offers better value for your investment.
            </p>
          </div>

          {/* Pricing Table */}
          <div className="bg-card border rounded-lg overflow-hidden mb-12">
            <div className="grid grid-cols-3 gap-4 p-6 bg-muted/50 border-b">
              <div className="font-semibold">Plan</div>
              <div className="font-semibold text-center">Inloop</div>
              <div className="font-semibold text-center">{comparison.competitor}</div>
            </div>
            
            {/* Free Tier */}
            <div className="grid grid-cols-3 gap-4 p-6 border-b">
              <div className="font-medium">Free Tier</div>
              <div className="text-center">
                <div className="text-green-600 font-medium">Free forever</div>
                <div className="text-sm text-muted-foreground">William AI + 4 credits</div>
              </div>
              <div className="text-center">
                <div className="text-muted-foreground">7-day trial only</div>
              </div>
            </div>

            {/* Entry Level */}
            <div className="grid grid-cols-3 gap-4 p-6 border-b">
              <div className="font-medium">Entry (Individual)</div>
              <div className="text-center">
                <div className="text-primary font-medium">$10/month</div>
                <div className="text-sm text-muted-foreground">All features included</div>
              </div>
              <div className="text-center">
                <div className="text-muted-foreground">$39/month</div>
              </div>
            </div>

            {/* Mid-Tier */}
            <div className="grid grid-cols-3 gap-4 p-6 border-b">
              <div className="font-medium">Mid-Tier (Business)</div>
              <div className="text-center">
                <div className="text-primary font-medium">$50/month</div>
                <div className="text-sm text-muted-foreground">Higher credit limit</div>
              </div>
              <div className="text-center">
                <div className="text-muted-foreground">$65/month</div>
              </div>
            </div>

            {/* Premium */}
            <div className="grid grid-cols-3 gap-4 p-6">
              <div className="font-medium">Premium (Enterprise)</div>
              <div className="text-center">
                <div className="text-primary font-medium">Custom pricing</div>
                <div className="text-sm text-muted-foreground">Priority support</div>
              </div>
              <div className="text-center">
                <div className="text-muted-foreground">$199/month</div>
              </div>
            </div>
          </div>

          {/* Value Proposition */}
          <div className="text-center mb-12">
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-xl font-semibold mb-4 text-primary">
                Better Value, Better ROI
              </h3>
              <p className="text-muted-foreground">
                {pricingSection?.valueproposition || 
                 `Budget match: Inloop's $100 Starter vs ${comparison.competitor}'s higher pricing — more value, far more ROI.`}
              </p>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <Button size="lg" className="px-8">
              {pricingSection?.cta || "Try Inloop Free"}
            </Button>
            <p className="text-sm text-muted-foreground mt-4">
              No credit card required • Free forever plan available
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
