"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";

interface ComparisonPricingProps {
  comparison: Comparison;
}

export default function ComparisonPricing({
  comparison,
}: ComparisonPricingProps) {
  const pricingSection = parseMarkdownSection(
    comparison.content,
    "Pricing Comparison Section"
  );

  // Parse pricing table from markdown
  const parsePricingTable = (content: string) => {
    const tableMatch = content.match(/\| Plan.*?\n((?:\|.*?\n)*)/);
    if (!tableMatch) return [];

    const rows = tableMatch[1]
      .split("\n")
      .filter((row) => row.trim() && !row.includes("---"));

    return rows
      .map((row) => {
        const cells = row
          .split("|")
          .map((cell) => cell.trim())
          .filter((cell) => cell);
        if (cells.length >= 3) {
          return {
            plan: cells[0].replace(/\*\*/g, ""),
            inloop: cells[1],
            competitor: cells[2],
          };
        }
        return null;
      })
      .filter(Boolean);
  };

  const pricingData = parsePricingTable(comparison.content);

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            {pricingSection?.headline && (
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {pricingSection.headline}
              </h2>
            )}
            {pricingSection?.description && (
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {pricingSection.description}
              </p>
            )}
          </div>

          {/* Pricing Table */}
          <div className="bg-card border rounded-lg overflow-hidden mb-12">
            <div className="grid grid-cols-3 gap-4 p-6 bg-muted/50 border-b">
              <div className="font-semibold">Plan</div>
              <div className="font-semibold text-center">Inloop</div>
              <div className="font-semibold text-center">
                {comparison.competitor}
              </div>
            </div>

            {/* Dynamic pricing rows from markdown */}
            {pricingData.map((row: any, index: number) => (
              <div
                key={index}
                className={`grid grid-cols-3 gap-4 p-6 ${
                  index < pricingData.length - 1 ? "border-b" : ""
                }`}
              >
                <div className="font-medium">{row.plan}</div>
                <div className="text-center">
                  <div className="text-primary font-medium">{row.inloop}</div>
                </div>
                <div className="text-center">
                  <div className="text-muted-foreground">{row.competitor}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Value Proposition */}
          <div className="text-center mb-12">
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-xl font-semibold mb-4 text-primary">
                Better Value, Better ROI
              </h3>
              {pricingSection?.valueproposition && (
                <p className="text-muted-foreground">
                  {pricingSection.valueproposition}
                </p>
              )}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            {pricingSection?.cta && (
              <Button size="lg" className="px-8">
                {pricingSection.cta}
              </Button>
            )}
            <p className="text-sm text-muted-foreground mt-4">
              No credit card required • Free forever plan available
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
