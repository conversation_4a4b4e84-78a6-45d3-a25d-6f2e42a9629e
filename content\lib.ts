import { join } from "path";
import fs from "fs";
import matter from "gray-matter";
import { remark } from "remark";
import remarkGfm from "remark-gfm";
import remarkHeadingId from "remark-heading-id";
import remarkRehype from "remark-rehype";
import rehypeRaw from "rehype-raw";
import rehypeStringify from "rehype-stringify";

export type Blog = {
  coverImage: string;
  authorImage: string;
  author: string;
  title: string;
  metaTitle: string;
  description: string;
  content: string;
  slug: string;
  date: string;
  keywords?: string;
};

export type Comparison = {
  title: string;
  description: string;
  content: string;
  slug: string;
  canonical?: boolean;
  competitor: string;
};

const markdownToHtml = async (markdown: string) => {
  const result = await remark()
    .use(remarkGfm)
    .use(remarkHeadingId, { defaults: true })
    .use(remarkRehype, { allowDangerousHtml: true })
    .use(rehypeRaw)
    .use(rehypeStringify)
    .process(markdown);

  return result.toString();
};

const getDir = (path: string) => join(process.cwd(), path);
const BLOG_DIR = getDir("/content/blogs");
const COMPARISON_DIR = getDir("/content/comparison");

const getFileNames = (dir: string): string[] => {
  return fs.readdirSync(dir);
};

const getBlogFileNames = () => {
  return getFileNames(BLOG_DIR);
};

const getItemInPath = (filePath: string) => {
  const fileContent = fs.readFileSync(filePath, "utf8");
  const { data, content } = matter(fileContent);
  return { ...data, content };
};

const getBlog = (name: string): Blog => {
  const blog = getItemInPath(join(BLOG_DIR, name)) as Blog;
  blog.slug = name.replace(/\.md$/, "");
  return blog;
};

const getBlogBySlug = async (slug: string) => {
  const fileName = slug + ".md";
  const blog = getBlog(fileName);
  blog.content = await markdownToHtml(blog.content);
  return blog;
};

const getBlogs = () => {
  const names = getBlogFileNames();

  const items = names.map(getBlog);

  return items.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );
};

export const shortify = (text: string, maxLength = 60) => {
  if (text?.length <= maxLength) {
    return text;
  }

  return text?.substring(0, maxLength) + " ...";
};

// Comparison functions
const getComparisonFileNames = () => {
  return getFileNames(COMPARISON_DIR);
};

const getComparison = (name: string): Comparison => {
  const comparison = getItemInPath(join(COMPARISON_DIR, name)) as Comparison;
  comparison.slug = name.replace(/\.md$/, "");
  return comparison;
};

const getComparisonBySlug = async (slug: string) => {
  const fileName = slug + ".md";
  const comparison = getComparison(fileName);
  comparison.content = await markdownToHtml(comparison.content);
  return comparison;
};

const getComparisons = () => {
  const names = getComparisonFileNames();
  const items = names.map(getComparison);
  return items;
};

export {
  getBlogFileNames,
  getBlogs,
  getBlogBySlug,
  getComparisonFileNames,
  getComparisons,
  getComparisonBySlug,
};
