"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";

interface ComparisonHeroProps {
  comparison: Comparison;
}

export default function ComparisonHero({ comparison }: ComparisonHeroProps) {
  // Parse the hero section from markdown content
  const heroSection = parseMarkdownSection(comparison.content, "Hero Section");

  // Parse feature highlights from markdown
  const parseFeatureHighlights = (featuresText?: string) => {
    if (!featuresText) return [];

    const features = featuresText
      .split("\n")
      .filter((line) => line.trim().startsWith("- **"));
    return features
      .map((feature, index) => {
        const match = feature.match(/- \*\*(.*?)\*\*/);
        if (match) {
          return (
            <div
              key={index}
              className="bg-card/50 backdrop-blur-sm border rounded-lg p-4"
            >
              <span className="text-sm font-medium text-primary">
                {match[1]}
              </span>
            </div>
          );
        }
        return null;
      })
      .filter(Boolean);
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-background via-background/95 to-background/90 py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center text-center max-w-4xl mx-auto">
          {/* Headline */}
          {heroSection?.headline && (
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              {heroSection.headline}
            </h1>
          )}

          {/* Subheadline */}
          {heroSection?.subheadline && (
            <h2 className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl">
              {heroSection.subheadline}
            </h2>
          )}

          {/* Description */}
          {heroSection?.description && (
            <p className="text-lg text-muted-foreground mb-12 max-w-2xl leading-relaxed">
              {heroSection.description}
            </p>
          )}

          {/* Features Highlight */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-12">
            {parseFeatureHighlights(heroSection?.featureshighlight)}
          </div>

          {/* CTA */}
          {heroSection?.cta && (
            <Button size="lg" className="text-lg px-8 py-6">
              {heroSection.cta}
            </Button>
          )}
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/[0.03] via-transparent to-transparent pointer-events-none"></div>
    </section>
  );
}
