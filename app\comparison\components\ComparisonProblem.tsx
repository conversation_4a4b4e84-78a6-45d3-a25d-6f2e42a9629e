"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";

interface ComparisonProblemProps {
  comparison: Comparison;
}

export default function ComparisonProblem({
  comparison,
}: ComparisonProblemProps) {
  const problemSection = parseMarkdownSection(
    comparison.content,
    "Problem Section"
  );

  // Parse key features from markdown
  const parseKeyFeatures = (keyFeaturesText?: string) => {
    if (!keyFeaturesText) return [];

    const features = keyFeaturesText
      .split("\n")
      .filter((line) => line.trim().startsWith("- **"));
    return features
      .map((feature, index) => {
        const match = feature.match(/- \*\*(.*?)\*\*:?\s*(.*)/);
        if (match) {
          return (
            <FeatureCard key={index} title={match[1]} description={match[2]} />
          );
        }
        return null;
      })
      .filter(Boolean);
  };

  return (
    <section className="py-20 bg-card/20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Pain Points */}
          <div className="text-center mb-16">
            {problemSection?.painpoints && (
              <h2 className="text-3xl md:text-4xl font-bold mb-8">
                {problemSection.painpoints.split("\n")[0]}
              </h2>
            )}

            {/* Parse and display challenges from markdown */}
            {problemSection?.painpoints && (
              <div className="max-w-2xl mx-auto text-lg text-muted-foreground">
                {problemSection.painpoints
                  .split("\n")
                  .map((line: string, index: number) => {
                    if (
                      line.trim() &&
                      !line.includes("**Common challenges:**")
                    ) {
                      return (
                        <p key={index} className="mb-4">
                          {line}
                        </p>
                      );
                    }
                    return null;
                  })}
              </div>
            )}

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">
                  Lack of inspiration?
                </h3>
                <p className="text-muted-foreground">
                  Struggling to find fresh content ideas that resonate with your
                  audience.
                </p>
              </div>
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">
                  Research takes time?
                </h3>
                <p className="text-muted-foreground">
                  Spending hours researching topics and trends instead of
                  creating content.
                </p>
              </div>
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">
                  No personalized strategy?
                </h3>
                <p className="text-muted-foreground">
                  Creating generic content that doesn&apos;t align with your
                  brand voice.
                </p>
              </div>
            </div>

            <p className="text-lg text-muted-foreground mb-12">
              You&apos;re not the only one facing this.
            </p>
          </div>

          {/* Solution Introduction */}
          <div className="text-center">
            {problemSection?.solutionintroduction && (
              <h3 className="text-2xl md:text-3xl font-bold mb-6">
                {problemSection.solutionintroduction.split("\n")[0]}
              </h3>
            )}

            {problemSection?.solutionintroduction && (
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                {problemSection.solutionintroduction
                  .split("\n")
                  .slice(1)
                  .join(" ")}
              </p>
            )}

            {/* Key Features */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {parseKeyFeatures(problemSection?.keyfeatures)}
            </div>

            {problemSection?.cta && (
              <Button size="lg" className="text-lg px-8 py-6">
                {problemSection.cta}
              </Button>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}

function FeatureCard({
  title,
  description,
}: {
  title: string;
  description: string;
}) {
  return (
    <div className="bg-background border rounded-lg p-6 text-left">
      <h4 className="font-semibold text-lg mb-3 text-primary">{title}</h4>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}
