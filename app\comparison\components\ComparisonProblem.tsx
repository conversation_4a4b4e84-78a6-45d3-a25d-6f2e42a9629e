"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";

interface ComparisonProblemProps {
  comparison: Comparison;
}

export default function ComparisonProblem({ comparison }: ComparisonProblemProps) {
  const problemSection = parseMarkdownSection(comparison.content, "Problem Section");
  
  return (
    <section className="py-20 bg-card/20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Pain Points */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">
              {problemSection?.painpoints || "Do you and your team struggle to build meaningful personalized content consistently for LinkedIn?"}
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">Lack of inspiration?</h3>
                <p className="text-muted-foreground">Struggling to find fresh content ideas that resonate with your audience.</p>
              </div>
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">Research takes time?</h3>
                <p className="text-muted-foreground">Spending hours researching topics and trends instead of creating content.</p>
              </div>
              <div className="bg-card border rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-3">No personalized strategy?</h3>
                <p className="text-muted-foreground">Creating generic content that doesn&apos;t align with your brand voice.</p>
              </div>
            </div>
            
            <p className="text-lg text-muted-foreground mb-12">
              You&apos;re not the only one facing this.
            </p>
          </div>
          
          {/* Solution Introduction */}
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Meet William, Inloop&apos;s AI Agent that acts as your personal content strategist
            </h3>
            
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              William helps you build personalized, highly engaging content that is tailored to your audience.
            </p>
            
            {/* Key Features */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              <FeatureCard 
                title="Magic Create"
                description="William creates a week's worth of personalized thought leadership content"
              />
              <FeatureCard 
                title="Topical Content"
                description="Craft from top industry news and leading blogs"
              />
              <FeatureCard 
                title="Template Library"
                description="Use viral template structures to shape content ideas into engaging posts"
              />
              <FeatureCard 
                title="Inspiration Hub"
                description="Access a library of over a million trending and viral posts"
              />
              <FeatureCard 
                title="Content Calendar"
                description="View your personal and team content calendar in a single window"
              />
            </div>
            
            <Button size="lg" className="text-lg px-8 py-6">
              Try Inloop Free
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

function FeatureCard({ title, description }: { title: string; description: string }) {
  return (
    <div className="bg-background border rounded-lg p-6 text-left">
      <h4 className="font-semibold text-lg mb-3 text-primary">{title}</h4>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}
