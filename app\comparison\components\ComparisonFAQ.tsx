"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";
import { cn } from "@/lib/utils";

interface ComparisonFAQProps {
  comparison: Comparison;
}

type FAQItem = {
  question: string;
  answer: string;
};

export default function ComparisonFAQ({ comparison }: ComparisonFAQProps) {
  const faqSection = parseMarkdownSection(comparison.content, "FAQ Section");
  
  // Default FAQ items - in real implementation, these would be parsed from markdown
  const defaultFaqItems: FAQItem[] = [
    {
      question: `How is Inloop different from ${comparison.competitor}?`,
      answer: `Inloop features <PERSON>, an AI Agent that proactively creates personalized content for you, while ${comparison.competitor} focuses primarily on writing assistance and formatting. Inloop provides end-to-end content strategy, from ideation to publication.`
    },
    {
      question: "Can I migrate my content from other platforms?",
      answer: "Yes, Inloop makes it easy to import your existing content and continue building on your established voice and style. Our AI learns from your previous posts to maintain consistency."
    },
    {
      question: "How much time will I save with Inloop?",
      answer: "Most users report saving 4-6 hours per week on content creation. William handles research, ideation, and drafting, so you can focus on engagement and strategy."
    },
    {
      question: "Is there a learning curve?",
      answer: "Inloop is designed to be intuitive from day one. Most users are creating high-quality content within their first session. Our AI Agent William guides you through the process."
    },
    {
      question: "What kind of support do you offer?",
      answer: "We provide comprehensive support including onboarding assistance, best practices guides, and responsive customer service. Premium plans include priority support and dedicated account management."
    },
    {
      question: "Can I try Inloop before committing?",
      answer: "Absolutely! Inloop offers a free forever plan with William AI and 4 credits to get you started. No credit card required, and you can upgrade anytime as your needs grow."
    }
  ];

  return (
    <section className="py-20 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground">
              Everything you need to know about choosing Inloop over {comparison.competitor}
            </p>
          </div>

          {/* FAQ Items */}
          <div className="space-y-4">
            {defaultFaqItems.map((item, index) => (
              <FAQItem key={index} question={item.question} answer={item.answer} />
            ))}
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <div className="bg-card border rounded-lg p-8">
              <h3 className="text-xl font-semibold mb-4">
                Still have questions?
              </h3>
              <p className="text-muted-foreground mb-6">
                Our team is here to help you make the right choice for your LinkedIn content strategy.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
                  Contact Sales
                </button>
                <button className="px-6 py-3 border border-border rounded-lg font-medium hover:bg-muted/50 transition-colors">
                  Schedule Demo
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function FAQItem({ question, answer }: FAQItem) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="bg-card/60 border border-primary/20 bg-gradient-to-br from-primary/5 to-background/20 shadow-sm backdrop-blur-xs rounded-xl overflow-hidden">
      <button
        className="w-full px-6 py-5 flex items-center justify-between text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-lg md:text-xl font-medium text-foreground">
          {question}
        </h3>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1.0] }}
        >
          <ChevronDown className="w-5 h-5 text-primary" />
        </motion.div>
      </button>

      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            key="content"
            initial="collapsed"
            animate="open"
            exit="collapsed"
            variants={{
              open: { opacity: 1, height: "auto", marginTop: "0.5rem" },
              collapsed: { opacity: 0, height: 0, marginTop: 0 },
            }}
            transition={{
              duration: 0.5,
              ease: [0.25, 0.1, 0.25, 1.0],
            }}
            className="px-6 overflow-hidden"
          >
            <motion.div
              variants={{
                collapsed: { opacity: 0, y: -10 },
                open: { opacity: 1, y: 0 },
              }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="pb-6 text-muted-foreground/90 leading-relaxed"
            >
              <p>{answer}</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
