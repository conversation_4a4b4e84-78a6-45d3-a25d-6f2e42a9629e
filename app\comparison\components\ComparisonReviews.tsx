"use client";

import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";

interface ComparisonReviewsProps {
  comparison: Comparison;
}

export default function ComparisonReviews({
  comparison,
}: ComparisonReviewsProps) {
  const reviewsSection = parseMarkdownSection(
    comparison.content,
    "Review Section"
  );

  // Only show reviews section if content exists in markdown
  if (!reviewsSection) {
    return null;
  }

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center">
            {reviewsSection?.headline && (
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {reviewsSection.headline}
              </h2>
            )}
            {reviewsSection?.subheadline && (
              <h3 className="text-xl text-muted-foreground mb-4">
                {reviewsSection.subheadline}
              </h3>
            )}
            {reviewsSection?.description && (
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
                {reviewsSection.description}
              </p>
            )}
            {reviewsSection?.cta && (
              <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
                {reviewsSection.cta}
              </button>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
