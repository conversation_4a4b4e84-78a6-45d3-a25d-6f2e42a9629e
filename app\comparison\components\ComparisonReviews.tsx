"use client";

import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";
import { Star } from "lucide-react";

interface ComparisonReviewsProps {
  comparison: Comparison;
}

export default function ComparisonReviews({ comparison }: ComparisonReviewsProps) {
  const reviewsSection = parseMarkdownSection(comparison.content, "Reviews Section");
  
  // Mock review data - in real implementation, this would be parsed from markdown
  const inloopReviews = [
    {
      name: "<PERSON>",
      role: "Marketing Director",
      company: "TechCorp",
      rating: 5,
      review: "<PERSON> has transformed our LinkedIn strategy. Our engagement increased by 300% in just 2 months.",
      avatar: "/avatars/sarah.jpg"
    },
    {
      name: "<PERSON>",
      role: "Sales Leader",
      company: "GrowthCo",
      rating: 5,
      review: "The AI agent creates content that actually sounds like me. It's incredible how personalized it is.",
      avatar: "/avatars/michael.jpg"
    },
    {
      name: "<PERSON>",
      role: "Founder",
      company: "StartupXYZ",
      rating: 5,
      review: "Inloop saves me 5+ hours per week on content creation. The ROI is undeniable.",
      avatar: "/avatars/emily.jpg"
    }
  ];

  const competitorReviews = [
    {
      name: "<PERSON>",
      role: "Content Manager",
      company: "MediaCorp",
      rating: 3,
      review: `${comparison.competitor} helps with writing, but I still spend hours creating content from scratch.`,
      avatar: "/avatars/david.jpg"
    },
    {
      name: "Lisa Wang",
      role: "Marketing Specialist",
      company: "BrandCo",
      rating: 3,
      review: "Good formatting tools, but lacks the AI intelligence I need for strategic content.",
      avatar: "/avatars/lisa.jpg"
    }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              What Users Are Saying
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Real feedback from professionals who've experienced both platforms.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Inloop Reviews */}
            <div>
              <div className="flex items-center gap-3 mb-8">
                <h3 className="text-2xl font-semibold text-primary">Inloop</h3>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="text-sm text-muted-foreground ml-2">4.9/5</span>
                </div>
              </div>
              
              <div className="space-y-6">
                {inloopReviews.map((review, index) => (
                  <ReviewCard key={index} review={review} />
                ))}
              </div>
            </div>

            {/* Competitor Reviews */}
            <div>
              <div className="flex items-center gap-3 mb-8">
                <h3 className="text-2xl font-semibold">{comparison.competitor}</h3>
                <div className="flex items-center gap-1">
                  {[...Array(3)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                  {[...Array(2)].map((_, i) => (
                    <Star key={i + 3} className="h-5 w-5 text-gray-300" />
                  ))}
                  <span className="text-sm text-muted-foreground ml-2">3.2/5</span>
                </div>
              </div>
              
              <div className="space-y-6">
                {competitorReviews.map((review, index) => (
                  <ReviewCard key={index} review={review} />
                ))}
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="mt-16 text-center">
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-8 max-w-3xl mx-auto">
              <h3 className="text-xl font-semibold mb-4">
                The Verdict
              </h3>
              <p className="text-muted-foreground">
                Users consistently report higher satisfaction, better results, and significant time savings with Inloop's AI-powered approach compared to traditional writing assistance tools.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

interface ReviewCardProps {
  review: {
    name: string;
    role: string;
    company: string;
    rating: number;
    review: string;
    avatar: string;
  };
}

function ReviewCard({ review }: ReviewCardProps) {
  return (
    <div className="bg-card border rounded-lg p-6">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
          <span className="text-sm font-medium text-primary">
            {review.name.split(' ').map(n => n[0]).join('')}
          </span>
        </div>
        <div>
          <div className="font-medium">{review.name}</div>
          <div className="text-sm text-muted-foreground">
            {review.role} at {review.company}
          </div>
        </div>
        <div className="ml-auto flex items-center gap-1">
          {[...Array(review.rating)].map((_, i) => (
            <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          ))}
        </div>
      </div>
      <p className="text-muted-foreground leading-relaxed">
        "{review.review}"
      </p>
    </div>
  );
}
