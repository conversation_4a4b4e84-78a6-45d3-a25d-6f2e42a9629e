"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";
import { Check, X } from "lucide-react";

interface ComparisonFeaturesProps {
  comparison: Comparison;
}

export default function ComparisonFeatures({ comparison }: ComparisonFeaturesProps) {
  const propositionSection = parseMarkdownSection(comparison.content, "Proposition Section");
  
  // Mock comparison data - in real implementation, this would be parsed from markdown
  const comparisonData = [
    {
      feature: "AI Agent",
      inloop: "AI Content Strategist suggests and creates personalized content weekly",
      competitor: "No AI agent — focuses on writing assistance only",
      inloopHas: true,
      competitorHas: false
    },
    {
      feature: "Blank Canvas",
      inloop: "Craft posts instantly from YouTube URLs, docs, websites, and ideas",
      competitor: "Does not support creating posts from external content sources",
      inloopHas: true,
      competitorHas: false
    },
    {
      feature: "AI Capabilities",
      inloop: "AI Agent builds personalized content, saves over 4 hours per week",
      competitor: "No proactive AI content generation — purely manual drafting support",
      inloopHas: true,
      competitorHas: false
    },
    {
      feature: "Topical Authority",
      inloop: "Personalized feed of industry news, blogs, and trends",
      competitor: "Limited focus on trends, no real-time topical curation",
      inloopHas: true,
      competitorHas: false
    },
    {
      feature: "Team Collaboration",
      inloop: "Team calendar, personal + shared schedules",
      competitor: "Individual scheduling, no advanced team features",
      inloopHas: true,
      competitorHas: false
    }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Inloop is a better choice for fast-growing professionals and leaders
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compare features side by side and see why Inloop delivers better value for thought leadership content creation.
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-card border rounded-lg overflow-hidden mb-12">
            <div className="grid grid-cols-4 gap-4 p-6 bg-muted/50 border-b">
              <div className="font-semibold">Feature</div>
              <div className="font-semibold text-center">Inloop</div>
              <div className="font-semibold text-center">{comparison.competitor}</div>
              <div className="font-semibold text-center">Winner</div>
            </div>
            
            {comparisonData.map((row, index) => (
              <div key={index} className="grid grid-cols-4 gap-4 p-6 border-b last:border-b-0">
                <div className="font-medium">{row.feature}</div>
                <div className="text-sm">
                  <div className="flex items-start gap-2">
                    {row.inloopHas ? (
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    )}
                    <span>{row.inloop}</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-start gap-2">
                    {row.competitorHas ? (
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    )}
                    <span>{row.competitor}</span>
                  </div>
                </div>
                <div className="text-center">
                  <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                    ✓
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Feature by Feature Section */}
          <div className="space-y-12">
            <h3 className="text-2xl md:text-3xl font-bold text-center">
              Feature by Feature Analysis
            </h3>
            
            {/* AI Agent Feature */}
            <FeatureComparison
              title="AI Agent"
              inloopDescription="AI Agent William is your personal content strategist who curates weekly thought-leadership content using your brand voice, audience signals, trending topics, and proven templates — generating measurable 7× engagement growth."
              competitorDescription={`No AI agent — focuses on writing assistance tools, formatting, and analytics rather than proactive content strategy.`}
              competitor={comparison.competitor}
            />
            
            {/* Blank Canvas Feature */}
            <FeatureComparison
              title="Blank Canvas"
              inloopDescription="Paste a URL from YouTube, internal docs, or web pages and generate full LinkedIn posts instantly."
              competitorDescription="No support for content creation from external sources; you start drafts manually."
              competitor={comparison.competitor}
            />
            
            {/* Personalization Feature */}
            <FeatureComparison
              title="Personalization"
              inloopDescription="Content feed and AI-generated posts are deeply personalized to your audience, industry, and personal voice — including niche data and topical signals."
              competitorDescription="Offers writing frameworks but lacks true personalization for industry or audience context."
              competitor={comparison.competitor}
            />
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <Button size="lg" className="text-lg px-8 py-6">
              Try Inloop Free
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

function FeatureComparison({ 
  title, 
  inloopDescription, 
  competitorDescription, 
  competitor 
}: { 
  title: string; 
  inloopDescription: string; 
  competitorDescription: string;
  competitor: string;
}) {
  return (
    <div className="grid md:grid-cols-2 gap-8">
      <div className="bg-primary/5 border border-primary/20 rounded-lg p-6">
        <h4 className="font-semibold text-lg mb-3 text-primary">Inloop</h4>
        <p className="text-muted-foreground">{inloopDescription}</p>
      </div>
      <div className="bg-card border rounded-lg p-6">
        <h4 className="font-semibold text-lg mb-3">{competitor}</h4>
        <p className="text-muted-foreground">{competitorDescription}</p>
      </div>
    </div>
  );
}
