"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Comparison } from "@/content/lib";
import { parseMarkdownSection } from "@/lib/utils";
import { Check, X } from "lucide-react";

interface ComparisonFeaturesProps {
  comparison: Comparison;
}

export default function ComparisonFeatures({
  comparison,
}: ComparisonFeaturesProps) {
  const propositionSection = parseMarkdownSection(
    comparison.content,
    "Proposition Section"
  );

  // Parse comparison table from markdown
  const parseComparisonTable = (content: string) => {
    const tableMatch = content.match(/\| Feature.*?\n((?:\|.*?\n)*)/);
    if (!tableMatch) return [];

    const rows = tableMatch[1]
      .split("\n")
      .filter((row) => row.trim() && !row.includes("---"));

    return rows
      .map((row) => {
        const cells = row
          .split("|")
          .map((cell) => cell.trim())
          .filter((cell) => cell);
        if (cells.length >= 3) {
          return {
            feature: cells[0].replace(/\*\*/g, ""),
            inloop: cells[1],
            competitor: cells[2],
            inloopHas: true, // Assume Inloop has the feature
            competitorHas:
              !cells[2].toLowerCase().includes("no ") &&
              !cells[2].toLowerCase().includes("limited"),
          };
        }
        return null;
      })
      .filter((item): item is NonNullable<typeof item> => item !== null);
  };

  const comparisonData = parseComparisonTable(comparison.content);

  // Parse feature-by-feature comparisons from markdown
  const parseFeatureComparisons = (content: string, competitor: string) => {
    const featureSection = parseMarkdownSection(
      content,
      "Feature by Feature Section"
    );
    if (!featureSection) return null;

    const features = [];
    const featureNames = [
      "aiagent",
      "blankcanvas",
      "personalization",
      "teamcollaboration&scheduling",
      "pricing&roi",
      "usecasefit",
    ];

    for (const featureName of featureNames) {
      if (featureSection[featureName]) {
        const lines = featureSection[featureName]
          .split("\n")
          .filter((line: string) => line.trim());
        let inloopDesc = "";
        let competitorDesc = "";

        for (const line of lines) {
          if (line.startsWith("**Inloop:**")) {
            inloopDesc = line.replace("**Inloop:**", "").trim();
          } else if (line.startsWith(`**${competitor}:**`)) {
            competitorDesc = line.replace(`**${competitor}:**`, "").trim();
          }
        }

        if (inloopDesc && competitorDesc) {
          features.push({
            title: featureName
              .replace(/([A-Z])/g, " $1")
              .replace(/^./, (str) => str.toUpperCase()),
            inloopDescription: inloopDesc,
            competitorDescription: competitorDesc,
          });
        }
      }
    }

    return features.map((feature, index) => (
      <FeatureComparison
        key={index}
        title={feature.title}
        inloopDescription={feature.inloopDescription}
        competitorDescription={feature.competitorDescription}
        competitor={competitor}
      />
    ));
  };

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Inloop is a better choice for fast-growing professionals and
              leaders
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Compare features side by side and see why Inloop delivers better
              value for thought leadership content creation.
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-card border rounded-lg overflow-hidden mb-12">
            <div className="grid grid-cols-4 gap-4 p-6 bg-muted/50 border-b">
              <div className="font-semibold">Feature</div>
              <div className="font-semibold text-center">Inloop</div>
              <div className="font-semibold text-center">
                {comparison.competitor}
              </div>
              <div className="font-semibold text-center">Winner</div>
            </div>

            {comparisonData.map((row, index) => (
              <div
                key={index}
                className="grid grid-cols-4 gap-4 p-6 border-b last:border-b-0"
              >
                <div className="font-medium">{row.feature}</div>
                <div className="text-sm">
                  <div className="flex items-start gap-2">
                    {row.inloopHas ? (
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    )}
                    <span>{row.inloop}</span>
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-start gap-2">
                    {row.competitorHas ? (
                      <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    )}
                    <span>{row.competitor}</span>
                  </div>
                </div>
                <div className="text-center">
                  <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                    ✓
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Feature by Feature Section */}
          <div className="space-y-12">
            <h3 className="text-2xl md:text-3xl font-bold text-center">
              {parseMarkdownSection(
                comparison.content,
                "Feature by Feature Section"
              )?.headline || "Feature by Feature Analysis"}
            </h3>

            {/* Parse feature comparisons from markdown */}
            {parseFeatureComparisons(comparison.content, comparison.competitor)}
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <Button size="lg" className="text-lg px-8 py-6">
              Try Inloop Free
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

function FeatureComparison({
  title,
  inloopDescription,
  competitorDescription,
  competitor,
}: {
  title: string;
  inloopDescription: string;
  competitorDescription: string;
  competitor: string;
}) {
  return (
    <div className="grid md:grid-cols-2 gap-8">
      <div className="bg-primary/5 border border-primary/20 rounded-lg p-6">
        <h4 className="font-semibold text-lg mb-3 text-primary">Inloop</h4>
        <p className="text-muted-foreground">{inloopDescription}</p>
      </div>
      <div className="bg-card border rounded-lg p-6">
        <h4 className="font-semibold text-lg mb-3">{competitor}</h4>
        <p className="text-muted-foreground">{competitorDescription}</p>
      </div>
    </div>
  );
}
